# Discussion API Examples

## Discussion List Response with Ordering

When a user fetches their discussion list via `GET /api/discussions/threads/`, the response will be ordered with participant discussions first, then public discussions.

### Example Response

```json
{
  "results": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "title": "My Personal Discussion",
      "description": "Personal development and growth strategies discussion",
      "is_private": false,
      "discourse_type": "discussions",
      "is_participant": true,
      "participants": ["user-123"],
      "participants_details": [
        {
          "id": "user-123",
          "email": "<EMAIL>",
          "first_name": "<PERSON>",
          "last_name": "<PERSON><PERSON>",
          "nickname": "johndoe",
          "picture": "https://example.com/photos/tiny/john.jpg"
        }
      ],
      "last_message": {
        "id": "msg-001",
        "body": "Latest message in my discussion",
        "author_details": { /* ... */ },
        "created": "2024-01-15T10:30:00Z"
      },
      "created": "2024-01-15T09:00:00Z"
    },
    {
      "id": "550e8400-e29b-41d4-a716-446655440002",
      "title": "Team Discussion",
      "description": "Weekly team sync and project updates",
      "is_private": false,
      "discourse_type": "discussions",
      "is_participant": true,
      "participants": ["user-123", "user-456"],
      "participants_details": [
        { /* John's details */ },
        { /* Jane's details */ }
      ],
      "last_message": {
        "id": "msg-002",
        "body": "Team discussion message",
        "author_details": { /* ... */ },
        "created": "2024-01-14T15:20:00Z"
      },
      "created": "2024-01-14T08:00:00Z"
    },
    {
      "id": "550e8400-e29b-41d4-a716-446655440003",
      "title": "Alice's Discussion",
      "description": null,
      "is_private": false,
      "discourse_type": "discussions",
      "is_participant": false,
      "participants": ["user-789"],
      "participants_details": [
        {
          "id": "user-789",
          "email": "<EMAIL>",
          "first_name": "Alice",
          "last_name": "Smith",
          "nickname": "alice",
          "picture": "https://example.com/photos/tiny/alice.jpg"
        }
      ],
      "last_message": {
        "id": "msg-003",
        "body": "Public discussion message",
        "author_details": { /* ... */ },
        "created": "2024-01-16T12:00:00Z"
      },
      "created": "2024-01-16T11:00:00Z"
    }
  ]
}
```

### Ordering Explanation

In the above example:

1. **"My Personal Discussion"** appears first because:
   - `is_participant: true` (priority = 0)
   - Created on 2024-01-15 (newer than Team Discussion)

2. **"Team Discussion"** appears second because:
   - `is_participant: true` (priority = 0)
   - Created on 2024-01-14 (older than My Personal Discussion)

3. **"Alice's Discussion"** appears last because:
   - `is_participant: false` (priority = 1)
   - Even though it's the newest discussion, non-participant discussions have lower priority

## Creating Discussions

### Create Private Discussion
```http
POST /api/discussions/threads/
Content-Type: application/json

{
  "title": "My Private Discussion",
  "description": "Private brainstorming space",
  "is_private": true
}
```

### Create Public Discussion
```http
POST /api/discussions/threads/
Content-Type: application/json

{
  "title": "Public Discussion Topic",
  "description": "Latest industry trends discussion",
  "is_private": false
}
```

### Create Discussion without Description
```http
POST /api/discussions/threads/
Content-Type: application/json

{
  "title": "Simple Discussion",
  "is_private": false
}
```

### Create Message in Discussion
```http
POST /api/discussions/{thread_id}/messages/
Content-Type: application/json

{
  "body": "This is a new message in the discussion"
}
```

### Create Reply in Discussion
```http
POST /api/discussions/{thread_id}/messages/
Content-Type: application/json

{
  "parent_message": "message-uuid",
  "body": "This is a reply to the message"
}
```

### Create Discussion without Title (Error)
```http
POST /api/discussions/threads/
Content-Type: application/json

{
  "is_private": false
}
```

**Response (400 Bad Request):**
```json
{
  "detail": "Discussion title is required"
}
```

## WebSocket Connections

### Connect to Discussion List Updates
```javascript
const ws = new WebSocket('wss://api.example.com/ws/discussions/?token=your_jwt_token');

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  if (data.type === 'thread_list') {
    // Initial list of discussions (ordered as described above)
    console.log('Discussions:', data.threads);
  } else if (data.type === 'thread_update') {
    // Real-time updates to discussions
    console.log('Discussion updated:', data.thread);
  }
};
```

### Connect to Specific Discussion
```javascript
const threadId = '550e8400-e29b-41d4-a716-************';
const ws = new WebSocket(`wss://api.example.com/ws/discussions/${threadId}/?token=your_jwt_token`);

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  if (data.type === 'history') {
    // Initial message history with nested replies
    console.log('Message history:', data.messages);
  } else if (data.type === 'message') {
    // New message received
    console.log('New message:', data.message);
  }
};

// Send a message
ws.send(JSON.stringify({
  type: 'message',
  body: 'Hello everyone!'
}));

// Send a reply
ws.send(JSON.stringify({
  type: 'message',
  body: 'Great point!',
  parent_message: 'msg-001'
}));
```

## Key Differences from Chats

| Feature | Chats | Discussions |
|---------|-------|-------------|
| **UI Style** | Instant messaging | Forum-like |
| **Public Access** | Participants only | All authenticated users |
| **Title Visibility** | Private titles hidden | All titles visible |
| **Creation** | Requires multiple participants | Can start with 1 participant |
| **Ordering** | By creation date only | Participant threads first |
| **Title Requirement** | Optional (auto-generated) | **Mandatory** (validation error if missing) |
