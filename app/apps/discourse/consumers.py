import json
import uuid

from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async

from django.contrib.auth import get_user_model
from django.core.serializers.json import DjangoJSONEncoder

from apps.discourse.models import (
    ChatMessage,
    ChatThread,
)
from apps.discourse.serializers import (
    ChatMessageSerializer,
    ChatThreadSerializer,
)
from apps.discourse.cache import ChatCacheManager

User = get_user_model()


class UUIDEncoder(DjangoJSONEncoder):
    """
    JSON encoder that handles UUID objects by converting them to strings.
    """
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        return super().default(obj)


class ChatConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for individual chat threads.
    """
    async def connect(self):
        try:
            self.user = self.scope['user']
            self.thread_id = self.scope['url_route']['kwargs']['thread_id']
            self.room_group_name = f'thread_{self.thread_id}'

            if self.user.is_anonymous:
                await self.close(code=4001)
                return

            is_participant = await self.is_thread_participant()
            if not is_participant:
                await self.close(code=4003)
                return

            # Join room group
            await self.channel_layer.group_add(
                self.room_group_name,
                self.channel_name
            )

            await self.accept()

            # Send last 50 messages to the newly connected client
            messages = await self.get_thread_messages()
            if messages:
                await self.send(text_data=json.dumps({
                    'type': 'history',
                    'messages': messages
                }, cls=UUIDEncoder))
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"WebSocket connection error: {str(e)}")
            await self.close(code=4000)

    async def disconnect(self, close_code):
        # Leave room group
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

    async def receive(self, text_data):
        """
        Receive message from WebSocket.
        """
        try:
            data = json.loads(text_data)
            message_type = data.get('type', 'message')

            if message_type == 'message':
                # Create a new message
                message = await self.create_message(data.get('message', ''))

                # Send message to room group
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'chat_message',
                        'message': message
                    }
                )
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }, cls=UUIDEncoder))

    async def chat_message(self, event):
        """
        Receive message from room group and send to WebSocket.
        """
        message = event['message']

        await self.send(text_data=json.dumps({
            'type': 'message',
            'message': message
        }, cls=UUIDEncoder))

    @database_sync_to_async
    def is_thread_participant(self):
        """
        Check if the user is a participant in this thread.
        """
        try:
            thread = ChatThread.objects.get(id=self.thread_id)
            return thread.participants.filter(id=self.user.id).exists() or not thread.is_private
        except ChatThread.DoesNotExist:
            return False

    @database_sync_to_async
    def create_message(self, body):
        """
        Create a new message in the database.
        """
        thread = ChatThread.objects.get(id=self.thread_id)
        message = ChatMessage.objects.create(
            thread=thread,
            author=self.user,
            body=body
        )
        # Cache the message
        return ChatCacheManager.cache_message(message)

    @database_sync_to_async
    def get_thread_messages(self):
        """
        Get the last 50 messages for this thread.
        """
        # Try to get from cache first
        cached_messages = ChatCacheManager.get_thread_messages(self.thread_id)
        if cached_messages is not None:
            return cached_messages

        # If not in cache, get from database
        messages = ChatMessage.objects.filter(thread_id=self.thread_id).order_by('-created')[:50]
        if messages:
            # Cache and return the messages
            return ChatCacheManager.cache_thread_messages(self.thread_id, messages)
        return []


class ChatListConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for receiving updates about chat threads.
    """
    async def connect(self):
        try:
            self.user = self.scope['user']

            # Check if user is authenticated
            if self.user.is_anonymous:
                await self.close(code=4001)
                return

            self.room_group_name = f'user_{self.user.id}_threads'

            # Join room group
            await self.channel_layer.group_add(
                self.room_group_name,
                self.channel_name
            )

            await self.accept()

            # Send current threads to the newly connected client
            threads = await self.get_user_threads()
            await self.send(text_data=json.dumps({
                'type': 'thread_list',
                'threads': threads
            }, cls=UUIDEncoder))
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"WebSocket connection error in ChatListConsumer: {str(e)}")
            await self.close(code=4000)

    async def disconnect(self, close_code):
        # Leave room group
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

    async def thread_update(self, event):
        """
        Receive thread update from room group and send to WebSocket.
        """
        thread = event['thread']

        # Send thread update to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'thread_update',
            'thread': thread
        }, cls=UUIDEncoder))

    @database_sync_to_async
    def get_user_threads(self):
        """
        Get all threads for this user.
        """
        threads = ChatThread.objects.filter(participants=self.user)
        return ChatThreadSerializer(threads, many=True).data
