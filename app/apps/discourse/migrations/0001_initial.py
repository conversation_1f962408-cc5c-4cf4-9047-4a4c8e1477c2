# Generated by Django 5.1.4 on 2025-05-29 12:09

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatThread',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(blank=True, max_length=255)),
                ('is_private', models.BooleanField(default=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='ThreadParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('joined_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('thread', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='thread_participants', to='discourse.chatthread')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='thread_memberships', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='chatthread',
            name='participants',
            field=models.ManyToManyField(related_name='chat_threads', through='discourse.ThreadParticipant', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='ChatMessage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('body', models.TextField()),
                ('attachment', models.FileField(blank=True, null=True, upload_to='chat_attachments/')),
                ('created', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chat_messages', to=settings.AUTH_USER_MODEL)),
                ('thread', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='discourse.chatthread')),
            ],
            options={
                'ordering': ['created'],
                'indexes': [models.Index(fields=['thread', 'created'], name='discourse_c_thread__c2b94c_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='threadparticipant',
            index=models.Index(fields=['user'], name='discourse_t_user_id_d6ce99_idx'),
        ),
        migrations.AddIndex(
            model_name='threadparticipant',
            index=models.Index(fields=['thread', 'joined_at'], name='discourse_t_thread__74c1e3_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='threadparticipant',
            unique_together={('thread', 'user')},
        ),
        migrations.AddIndex(
            model_name='chatthread',
            index=models.Index(fields=['created'], name='discourse_c_created_7697fe_idx'),
        ),
    ]
