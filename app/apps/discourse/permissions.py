from rest_framework import permissions



class IsThreadParticipant(permissions.BasePermission):
    """
    Permission to only allow participants of a thread to access it.
    For discussions: public discussions are accessible to all authenticated users.
    """

    def has_object_permission(self, request, view, obj):
        # For discussions: allow access to public discussions or if user is participant
        if obj.discourse_type == 'discussions':
            return not obj.is_private or request.user in obj.participants.all()
        # For chats: only participants can access
        return request.user in obj.participants.all()


class IsMessageThreadParticipant(permissions.BasePermission):
    """
    Permission to only allow participants of a thread to access its messages.
    For discussions: public discussions are accessible to all authenticated users.
    """

    def has_permission(self, request, view):
        # For list view, check if thread_id is provided and user has access
        thread_id = request.query_params.get('thread')
        if thread_id:
            from apps.discourse.models import ChatThread
            try:
                thread = ChatThread.objects.get(id=thread_id)
                if thread.discourse_type == 'discussions':
                    # For discussions: allow access to public discussions or if user is participant
                    return not thread.is_private or request.user in thread.participants.all()
                else:
                    # For chats: only participants can access
                    return request.user.chat_threads.filter(id=thread_id).exists()
            except ChatThread.DoesNotExist:
                return False
        return True  # Will be checked in has_object_permission

    def has_object_permission(self, request, view, obj):
        # Check if the user has access to the message's thread
        thread = obj.thread
        if thread.discourse_type == 'discussions':
            # For discussions: allow access to public discussions or if user is participant
            return not thread.is_private or request.user in thread.participants.all()
        else:
            # For chats: only participants can access
            return request.user in thread.participants.all()
