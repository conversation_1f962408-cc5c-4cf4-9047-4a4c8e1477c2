from django.urls import re_path

from apps.discourse.consumers import (
    Chat<PERSON>onsumer,
    ChatListConsumer,
)
from apps.discourse.test_consumers import (
    TestWebSocketConsumer,
    AuthTestWebSocketConsumer,
)



websocket_urlpatterns = [
    re_path(r'ws/chat/$', ChatListConsumer.as_asgi()),
    re_path(r'ws/chat/(?P<thread_id>[0-9a-f-]+)/$', ChatConsumer.as_asgi()),

    # Test endpoints
    re_path(r'ws/test/$', TestWebSocketConsumer.as_asgi()),
    re_path(r'ws/auth-test/$', AuthTestWebSocketConsumer.as_asgi()),
]
