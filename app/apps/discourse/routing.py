from django.urls import re_path

from apps.discourse.consumers import (
    ChatConsumer,
    ChatListConsumer,
    TestWebSocketConsumer,
    AuthTestWebSocketConsumer,
    DebugWebSocketConsumer,
)



websocket_urlpatterns = [
    # Chat WebSocket endpoints
    re_path(r'^ws/chat/$', ChatListConsumer.as_asgi()),
    re_path(r'^ws/chat/(?P<thread_id>[0-9a-f\-]+)/$', ChatConsumer.as_asgi()),

    # Discussion WebSocket endpoints (reusing the same consumers)
    re_path(r'^ws/discussions/$', ChatListConsumer.as_asgi()),
    re_path(r'^ws/discussions/(?P<thread_id>[0-9a-f-]+)/$', ChatConsumer.as_asgi()),

    # Test endpoints
    re_path(r'^ws/test/$', TestWebSocketConsumer.as_asgi()),
    re_path(r'^ws/auth-test/$', AuthTestWebSocketConsumer.as_asgi()),
    re_path(r'^ws/debug/$', DebugWebSocketConsumer.as_asgi()),
]
