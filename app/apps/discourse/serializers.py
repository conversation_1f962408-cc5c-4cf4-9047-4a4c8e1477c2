from rest_framework import serializers
from django.contrib.auth import get_user_model

from apps.discourse.cache import ChatCacheManager
from apps.discourse.models import (
    ChatMessage,
    ChatThread,
    ThreadParticipant,
)



User = get_user_model()



class UserChatSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id',
                  'email',
                  'first_name',
                  'last_name']


class ChatMessageSerializer(serializers.ModelSerializer):
    author_details = UserChatSerializer(source='author', read_only=True)
    attachment_url = serializers.SerializerMethodField()

    class Meta:
        model = ChatMessage
        fields = ['id',
                  'thread',
                  'author',
                  'author_details',
                  'body',
                  'attachment',
                  'attachment_url',
                  'created']
        read_only_fields = ['author', 'created']

    def get_attachment_url(self, obj):
        if obj.attachment:
            return obj.attachment.url
        return None

    def create(self, validated_data):
        # Set the author to the current user
        validated_data['author'] = self.context['request'].user
        return super().create(validated_data)


class ThreadParticipantSerializer(serializers.ModelSerializer):
    user_details = UserChatSerializer(source='user', read_only=True)

    class Meta:
        model = ThreadParticipant
        fields = ['id',
                  'user',
                  'user_details',
                  'joined_at']


class ChatThreadSerializer(serializers.ModelSerializer):
    participants = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=User.objects.all(),
        required=False
    )
    participants_details = UserChatSerializer(source='participants', many=True, read_only=True)
    last_message = serializers.SerializerMethodField()
    recent_messages = serializers.SerializerMethodField()

    class Meta:
        model = ChatThread
        fields = ['id',
                  'title',
                  'is_private',
                  'participants',
                  'participants_details',
                  'last_message',
                  'recent_messages',
                  'created']

    def get_last_message(self, obj):
        last_message = obj.messages.order_by('-created').first()
        if last_message:
            return ChatMessageSerializer(last_message).data
        return None

    def get_recent_messages(self, obj):
        """
        Get recent messages for this thread.
        Only included when specifically requested with include_recent=true.
        """
        request = self.context.get('request')
        if not request or request.query_params.get('include_recent', 'false').lower() != 'true':
            return None

        cached_messages = ChatCacheManager.get_thread_messages(obj.id)
        if cached_messages is not None:
            return cached_messages

        recent_messages = obj.messages.order_by('-created')[:30]
        if recent_messages:
            return ChatCacheManager.cache_thread_messages(obj.id, recent_messages)
        return []

    def create(self, validated_data):
        participants = validated_data.pop('participants', [])
        # Always include the current user as a participant
        if self.context['request'].user not in participants:
            participants.append(self.context['request'].user)

        thread = ChatThread.objects.create(**validated_data)

        for user in participants:
            ThreadParticipant.objects.create(thread=thread, user=user)

        return thread

    def update(self, instance, validated_data):
        participants = validated_data.pop('participants', None)

        # Update thread fields
        instance = super().update(instance, validated_data)

        # Update participants if provided
        if participants is not None:
            # Ensure current user remains a participant
            if self.context['request'].user not in participants:
                participants.append(self.context['request'].user)

            # Get current participants
            current_participants = set(instance.participants.all())
            new_participants = set(participants)

            # Remove participants not in the new list
            to_remove = current_participants - new_participants
            for user in to_remove:
                ThreadParticipant.objects.filter(thread=instance, user=user).delete()

            # Add new participants
            to_add = new_participants - current_participants
            for user in to_add:
                ThreadParticipant.objects.create(thread=instance, user=user)

        return instance
