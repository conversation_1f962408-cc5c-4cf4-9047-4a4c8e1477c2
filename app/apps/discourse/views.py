from rest_framework import (
    filters,
    permissions,
    status,
    viewsets,
)
from rest_framework.response import Response
from rest_framework.decorators import action

from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db.models import Prefetch, Q
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

from apps.discourse.models import (
    ChatMessage,
    ChatThread,
    ThreadParticipant,
)
from apps.discourse.serializers import (
    ChatMessageSerializer,
    ChatThreadSerializer,
)
from apps.discourse.permissions import (
    IsMessageThreadParticipant,
    IsThreadParticipant,
)
from apps.discourse.cache import ChatCacheManager


User = get_user_model()


class ChatThreadViewSet(viewsets.ModelViewSet):
    """
    API endpoint for chat threads.
    """
    serializer_class = ChatThreadSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'

    def get_queryset(self):
        user = self.request.user

        # Filter threads where the user is a participant
        queryset = ChatThread.objects.filter(participants=user)

        # Add public threads if requested
        include_public = self.request.query_params.get('include_public', 'false').lower() == 'true'
        if include_public:
            queryset = queryset.union(
                ChatThread.objects.filter(is_private=False)
            )

        # Optimize with prefetch_related
        queryset = queryset.prefetch_related(
            'participants',
            Prefetch(
                'messages',
                queryset=ChatMessage.objects.order_by('-created')[:1],
                to_attr='last_messages'
            )
        ).order_by('-created')

        return queryset

    @action(detail=True, methods=['get'])
    def messages(self, request, id=None):
        """
        Get messages for a specific thread with pagination.
        """
        thread = self.get_object()

        # Get pagination parameters
        limit = int(request.query_params.get('limit', 30))
        offset = int(request.query_params.get('offset', 0))

        # Try to get from cache for first page
        if offset == 0:
            cached_messages = ChatCacheManager.get_thread_messages(thread.id, limit)
            if cached_messages is not None:
                total_count = ChatMessage.objects.filter(thread=thread).count()
                return Response({
                    'results': cached_messages,
                    'total': total_count,
                    'limit': limit,
                    'offset': offset,
                })

        # Get messages from database with pagination
        messages = ChatMessage.objects.filter(thread=thread).order_by('-created')[offset:offset+limit]
        serializer = ChatMessageSerializer(messages, many=True)

        if offset == 0 and messages:
            ChatCacheManager.cache_thread_messages(thread.id, messages)

        total_count = ChatMessage.objects.filter(thread=thread).count()
        return Response({
            'results': serializer.data,
            'total': total_count,
            'limit': limit,
            'offset': offset,
        })

    def get_permissions(self):
        if self.action in ['retrieve', 'update', 'partial_update', 'destroy']:
            return [permissions.IsAuthenticated(), IsThreadParticipant()]
        return super().get_permissions()

    @action(detail=True, methods=['post'])
    def add_participant(self, request, id=None):
        thread = self.get_object()
        user_id = request.data.get('user_id')

        try:
            user = User.objects.get(id=user_id)
            if user in thread.participants.all():
                return Response(
                    {'detail': 'User is already a participant.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            ThreadParticipant.objects.create(thread=thread, user=user)
            return Response(
                {'detail': 'User added successfully.'},
                status=status.HTTP_200_OK
            )
        except User.DoesNotExist:
            return Response(
                {'detail': 'User not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def remove_participant(self, request, id=None):
        thread = self.get_object()
        user_id = request.data.get('user_id')

        try:
            user = User.objects.get(id=user_id)
            if user == request.user and thread.participants.count() <= 1:
                return Response(
                    {'detail': 'Cannot remove yourself as the only participant.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            participant = ThreadParticipant.objects.filter(thread=thread, user=user)
            if participant.exists():
                participant.delete()
                return Response(
                    {'detail': 'User removed successfully.'},
                    status=status.HTTP_200_OK
                )
            else:
                return Response(
                    {'detail': 'User is not a participant.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except User.DoesNotExist:
            return Response(
                {'detail': 'User not found.'},
                status=status.HTTP_404_NOT_FOUND
            )


class ChatMessageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for chat messages.
    """
    serializer_class = ChatMessageSerializer
    permission_classes = [permissions.IsAuthenticated, IsMessageThreadParticipant]

    def get_queryset(self):
        queryset = ChatMessage.objects.select_related('author', 'thread')

        # Filter by thread if provided
        thread_id = self.request.query_params.get('thread')
        if thread_id:
            limit = int(self.request.query_params.get('limit', 30))
            offset = int(self.request.query_params.get('offset', 0))

            if offset == 0:
                cached_messages = ChatCacheManager.get_thread_messages(thread_id, limit)
                if cached_messages is not None:
                    return ChatMessage.objects.none()

            queryset = queryset.filter(thread_id=thread_id).order_by('-created')

            if offset == 0:
                messages = queryset[:limit]
                if messages:
                    ChatCacheManager.cache_thread_messages(thread_id, messages)

        return queryset

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)

        message = ChatMessage.objects.get(id=serializer.data['id'])
        ChatCacheManager.cache_message(message)

        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def list(self, request, *args, **kwargs):
        thread_id = request.query_params.get('thread')
        if thread_id:
            limit = int(request.query_params.get('limit', 30))
            offset = int(request.query_params.get('offset', 0))

            # Only use cache for the first page
            if offset == 0:
                # Try to get cached messages
                cached_messages = ChatCacheManager.get_thread_messages(thread_id, limit)
                if cached_messages is not None:
                    # Calculate total count for pagination info
                    total_count = ChatMessage.objects.filter(thread_id=thread_id).count()

                    # Create a paginated response
                    return Response({
                        'results': cached_messages,
                        'total': total_count,
                        'limit': limit,
                        'offset': offset,
                    })

            # Get messages from database with pagination
            queryset = ChatMessage.objects.filter(thread_id=thread_id).order_by('-created')[offset:offset+limit]
            serializer = self.get_serializer(queryset, many=True)

            if offset == 0 and queryset:
                ChatCacheManager.cache_thread_messages(thread_id, queryset)

            total_count = ChatMessage.objects.filter(thread_id=thread_id).count()
            return Response({
                'results': serializer.data,
                'total': total_count,
                'limit': limit,
                'offset': offset,
            })

        # Fall back to database if not cached
        return super().list(request, *args, **kwargs)
