import uuid
from urllib.parse import urljoin, urlparse

from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.text import slugify

from config.exceptions import (
    InvalidRequest,
    UserPermissionDenied,
)
from apps.authentication.permissions import (
    IsStaff,
    ReadOnly,
    IsStaffOrOwnAccount,
)

from apps.users.serializers import (
    PublicProfileSerializer,
    PrivateProfileSerializer,
)
from apps.user_content.models import (
    UserContent,
    SessionUserContent,
)
from apps.user_content.serializers import (
    UserContentResultsSerializer,
    SessionUserContentResultsSerializer,
    SlugUserContentSerializer,
)
from apps.users.permissions import MixedPermissionViewSet
from utils.hubspot.users import (
    subscribe_user,
    unsubscribe_user,
    update_user_details as hubspot_update_user_details,
)
from utils.nodebb.auth import (
    update_user_profile_details as nodebb_update_user_details,
    update_user_picture as nodebb_update_user_picture,
)



User = get_user_model()


def upload_profile_image(request, profile, field, profile_field):
    image_file = request.FILES['file']
    image_file.name = slugify(
        f"{profile.full_name} {str(profile.user.id)} {profile_field}")
    setattr(profile, field, image_file)
    profile.save()

    picture_file = getattr(profile, field)
    picture_url = urljoin(picture_file.url, urlparse(picture_file.url).path)
    rand = uuid.uuid4().hex[:8]
    profile.private_details[profile_field] = f"{picture_url}?{rand}"
    profile.map_details()


class ProfileViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_authenticated = ['retrieve',
                            'details',
                            'favourites',
                            'courses',
                            'events',
                            'webinars']
        is_staff_or_own_account = ['retrieve',
                                   'update',
                                   'partial_update',
                                   'details',
                                   'social_networks',
                                   'interests',
                                   'favourites',
                                   'courses',
                                   'events',
                                   'webinars',
                                   'picture',
                                   'picture_tiny',
                                   'cover']
        if self.action in is_annonymous:
            return [ReadOnly()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff_or_own_account:
            return [IsStaffOrOwnAccount()]
        return [permission() for permission in self.permission_classes]

    def get_serializer_class(self):
        if self.request.user.is_staff:
            return PrivateProfileSerializer
        return PublicProfileSerializer

    def retrieve(self, request, *args, **kwargs):
        if kwargs.get('pk') == str(request.user.id):
            # fetches own profile
            serializer = PrivateProfileSerializer(request.user.profile, context={'request': request})
            return Response(serializer.data)
        else:
            user = get_object_or_404(User, pk=kwargs.get('pk'))
            serializer = self.get_serializer_class()(user.profile, context={'request': request})
            return Response(serializer.data)

    @action(detail=True, methods=['GET', 'PATCH'])
    def details(self, request, pk):
        if pk == str(request.user.id):
            profile = request.user.profile
            if request.method == 'PATCH':
                profile.map_details(request.data)
                hubspot_update_user_details(request.user)
                nodebb_update_user_details(request.user)
            return Response(PrivateProfileSerializer(profile).data)
        else:
            user = get_object_or_404(User, pk=pk)
            profile = user.profile
            return Response(self.get_serializer_class()(profile, context={'request': request}).data)

    @action(detail=True, methods=['PATCH'])
    def social_networks(self, request, pk):
        if pk != str(request.user.id):
            raise UserPermissionDenied()
        profile = request.user.profile
        profile.social_networks = request.data
        profile.save()

        return Response(profile.social_networks)

    @action(detail=True, methods=['GET', 'PATCH'])
    def interests(self, request, pk):
        if pk != str(request.user.id):
            raise UserPermissionDenied()
        profile = request.user.profile
        if request.method == 'PATCH':
            hubspot_ids_subscribed = {i.get('hubspot_list_id') for i in profile.interests.values()}
            hubspot_ids_new = {i.get('hubspot_list_id') for i in request.data.values()}
            hubspot_ids_to_unsubscribe = hubspot_ids_subscribed - hubspot_ids_new
            hubspot_ids_to_ignore = hubspot_ids_subscribed & hubspot_ids_new
            hubspot_ids_to_subscribe = hubspot_ids_new - hubspot_ids_to_ignore
            profile.interests = request.data
            profile.save()
            for hubspot_list_id in hubspot_ids_to_subscribe:
                subscribe_user(request.user, hubspot_list_id)
            for hubspot_list_id in hubspot_ids_to_unsubscribe:
                unsubscribe_user(request.user, hubspot_list_id)

        return Response(profile.interests)

    @action(detail=True, methods=['GET'])
    def favourites(self, request, pk):
        if pk != str(request.user.id):
            raise UserPermissionDenied()
        # TODO: iterate over these queries
        # the queryset is lazyloaded but it still performes multiple db hits
        user_content = UserContent.bookmarked_objects.filter(user=request.user)
        courses = user_content.filter(content_type='courses')
        events = user_content.filter(content_type__in=['events', 'clubs'])
        films = user_content.filter(content_type='films')
        podcasts = user_content.filter(content_type='podcasts')
        articles = user_content.filter(content_type='articles')
        webinars = user_content.filter(content_type='webinars')
        event_sessions = SessionUserContent.bookmarked_objects.filter(
            user=request.user, content_type='event-session')

        return Response({
            "courses": {
                "results": UserContentResultsSerializer(courses, many=True).data,
                "total": courses.count()
            },
            "clubs": {
                "results": [
                    *UserContentResultsSerializer(events, many=True).data,
                    *SessionUserContentResultsSerializer(
                        event_sessions, many=True).data,
                ],
                "total": events.count() + event_sessions.count()
            },
            "films": {
                "results": UserContentResultsSerializer(films, many=True).data,
                "total": films.count()
            },
            "podcasts": {
                "results": UserContentResultsSerializer(podcasts, many=True).data,
                "total": podcasts.count()
            },
            "articles": {
                "results": UserContentResultsSerializer(articles, many=True).data,
                "total": articles.count()
            },
            "webinars": {
                "results": UserContentResultsSerializer(webinars, many=True).data,
                "total": webinars.count()
            },
        })

    @action(detail=True, methods=['GET'])
    def courses(self, request, pk):
        if pk != str(request.user.id):
            raise UserPermissionDenied()
        # TODO: iterate over these queries
        # the queryset is lazyloaded but it still performes multiple db hits
        user_courses = request.user.content.select_related("enrolment").filter(
            content_type='courses', enrolment__isnull=False, is_active=True)

        completed_courses = user_courses.filter(completed=True)
        in_progress_courses = user_courses.filter(completed=False)

        return Response({
            "completed": {
                "results": UserContentResultsSerializer(
                    completed_courses, many=True).data,
                "total": completed_courses.count()
            },
            "inProgress": {
                "results": UserContentResultsSerializer(
                    in_progress_courses, many=True).data,
                "total": in_progress_courses.count()
            }
        })

    @action(detail=True, methods=['GET'])
    def events(self, request, pk):
        if pk != str(request.user.id):
            raise UserPermissionDenied()
        user_events = request.user.content.select_related("enrolment").filter(
            content_type__in=['events', 'clubs'], enrolment__isnull=False, is_active=True)

        completed_events = user_events.filter(completed=True)
        in_progress_events = user_events.filter(completed=False)
        completed_event_sessions = SessionUserContent.objects.filter(
            user=request.user, completed=True, content_type='event-session')

        return Response({
            "completed": {
                "results": [
                    *UserContentResultsSerializer(
                        completed_events, many=True).data,
                    *SessionUserContentResultsSerializer(
                        completed_event_sessions, many=True).data,
                ],
                "total": completed_events.count() + completed_event_sessions.count()
            },
            "inProgress": {
                "results": UserContentResultsSerializer(
                    in_progress_events, many=True).data,
                "total": in_progress_events.count()
            }
        })

    @action(detail=True, methods=['GET'])
    def webinars(self, request, pk):
        if pk != str(request.user.id):
            raise UserPermissionDenied()
        user_webinars = UserContent.objects.filter(
            user=request.user, content_type='webinars')
        in_progress_webinars = user_webinars.filter(
            object_data__endDateTime__gte=str(timezone.now()))
        completed_webinars = user_webinars.filter(
            object_data__endDateTime__lt=str(timezone.now()))

        return Response({
            "completed": {
                "results": UserContentResultsSerializer(
                    completed_webinars, many=True).data,
                "total": completed_webinars.count()
            },
            "inProgress": {
                "results": UserContentResultsSerializer(
                    in_progress_webinars, many=True).data,
                "total": in_progress_webinars.count()
            }
        })

    @action(detail=True, methods=['PATCH', 'POST'])
    def picture(self, request, pk):
        if pk != str(request.user.id):
            raise UserPermissionDenied()

        profile = request.user.profile
        upload_profile_image(
            request, profile, 'picture', 'profile_photo')
        nodebb_update_user_picture(request.user)

        return Response(PrivateProfileSerializer(profile).data)

    @action(detail=True, methods=['PATCH', 'POST'])
    def picture_tiny(self, request, pk):
        if pk != str(request.user.id):
            raise UserPermissionDenied()

        profile = request.user.profile
        upload_profile_image(
            request, profile, 'picture_tiny', 'profile_photo_tiny')

        return Response(PrivateProfileSerializer(profile).data)


    @action(detail=True, methods=['PATCH', 'POST'])
    def cover(self, request, pk):
        if pk != str(request.user.id):
            raise UserPermissionDenied()

        profile = request.user.profile
        upload_profile_image(
            request, profile, 'cover', 'cover_photo')

        return Response(PrivateProfileSerializer(profile).data)

    @action(detail=True, methods=['GET'])
    def mark_onboarding_completed(self, request, pk):
        if pk != str(request.user.id):
            raise UserPermissionDenied()

        profile = request.user.profile
        profile.onboarding_completed = True
        profile.save()

        return Response(PrivateProfileSerializer(profile).data)


class SettingsViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_authenticated = ['newsletters',
                            'privacy']
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        return [permission() for permission in self.permission_classes]

    @action(detail=False, methods=['GET', 'PATCH'])
    def privacy(self, request):
        if request.method == 'GET':
            return Response(request.user.profile.privacy_rules)
        elif request.method == 'PATCH':
            profile = request.user.profile
            profile.privacy_rules = request.data
            profile.save()
            profile.map_details()

            return Response(request.user.profile.privacy_rules)
        raise InvalidRequest(detail='Invalid request, please try again.')

    @action(detail=False, methods=['GET', 'PATCH'])
    def newsletters(self, request):
        if request.method == 'GET':
            return Response(request.user.profile.newsletters)
        elif request.method == 'PATCH':
            profile = request.user.profile
            profile.update_newsletters(request.data)

            return Response(profile.newsletters)
        raise InvalidRequest(detail='Invalid request, please try again.')
