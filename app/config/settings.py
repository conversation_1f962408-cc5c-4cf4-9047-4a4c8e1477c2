"""
Django settings for api project.

Generated by 'django-admin startproject' using Django 3.2.12.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""
import json
from datetime import timedelta
import itertools
from pathlib import Path
import os

from corsheaders.defaults import default_headers


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# DEBUG
# ------------------------------------------------------------------------------
DEBUG = os.environ.get('DEBUG') == 'True'
SECRET_KEY = os.environ.get('SECRET_KEY')
ENV = os.environ.get('DJANGO_ENV')
SENTRY_ENV = os.environ.get('SENTRY_ENV')
SENTRY_ENABLED = SENTRY_ENV in ['staging', 'production']

if SENTRY_ENABLED:
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration

    sentry_sdk.init(
        dsn=os.getenv('SENTRY_DSN'),
        environment=SENTRY_ENV,
        integrations=[DjangoIntegration()],
        traces_sample_rate=1.0,
        send_default_pii=True
    )


# DOMAINS
ALLOWED_HOSTS = ['*'] if DEBUG else json.loads(os.environ.get('ALLOWED_HOSTS'))

# Application definition

DJANGO_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'rest_framework_simplejwt',
    'corsheaders',
    'storages',
    'channels',
]

LOCAL_APPS = [
    'apps.authentication',
    'apps.users',
    'apps.user_content',
    'apps.user_data',
    'apps.dispatcher',
    'apps.discourse',

    'api.pages',
    'api.categories',
    'api.entities',
    'api.articles',
    'api.podcasts',
    'api.films',
    'api.courses',
    'api.events',
    'api.webinars',
]

HELPER_APPS = [
    'django_extensions',
    'debug_toolbar',
]

DEV_APPS = []

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',

    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

if DEBUG:
    INSTALLED_APPS += HELPER_APPS
    MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
    INTERNAL_IPS = ['127.0.0.1']

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'


# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('POSTGRES_NAME'),
        'USER': os.environ.get('POSTGRES_USER'),
        'PASSWORD': os.environ.get('POSTGRES_PASSWORD'),
        'HOST': os.environ.get('POSTGRES_HOST', 'db'),
        'PORT': os.environ.get('POSTGRES_PORT', 5432),
    }
}
if os.environ.get('DB_IGNORE_SSL') != 'True':
    DATABASES['default']['OPTIONS'] = {
        'sslmode': 'require'
    }


# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    # {
    #     'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    # },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        "OPTIONS": {
            "min_length": 10,
        },
    },
    # {
    #     'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    # },
    # {
    #     'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    # },
]

AUTH_USER_MODEL = 'users.User'

# DJANGO REST FRAMEWORK
# ------------------------------------------------------------------------------
REST_FRAMEWORK = {
    # 'TEST_REQUEST_DEFAULT_FORMAT': 'json',

    'UPLOADED_FILES_USE_URL': False,
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication'
    ],
    'DEFAULT_PERMISSION_CLASSES': [],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
        'rest_framework.parsers.FileUploadParser'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '5/minute',
        'contact': '1/hour'
    },
    # 'EXCEPTION_HANDLER': 'app.config.exceptions.sentry_drf_exception_handler',
}

# JSON Web Token Authentication
# ------------------------------------------------------------------------------
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=2),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,

    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,

    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',

    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',

    'JTI_CLAIM': 'jti',

    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(days=2),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=7),
}

# PASSWORD BYPASS
MASTER_PASSWORD_ENABLED = os.environ.get('MASTER_PASSWORD_ENABLED') == 'True'
MASTER_PASSWORD = os.environ.get('MASTER_PASSWORD')

# CACHE
if os.environ.get('CACHING_ENABLED') == 'True':
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.redis.RedisCache',
            'LOCATION': os.environ.get('REDIS_CACHE_URL'),
        }
    }
DEFAULT_CACHE_TTL = 60 * 60 * int(os.environ.get('DEFAULT_CACHE_TTL', '24'))
MINIMUM_CACHE_TTL = 60 * 60 * int(os.environ.get('MINIMUM_CACHE_TTL', '1'))
MAXIMUM_CACHE_TTL = 60 * 60 * int(os.environ.get('MAXIMUM_CACHE_TTL', '24'))

CELERY_BROKER_URL = os.environ.get('CELERY_BROKER')
CELERY_RESULT_BACKEND = os.environ.get('CELERY_BACKEND')

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = os.environ.get('LANGUAGE_CODE', 'en-us')

TIME_ZONE = os.environ.get('TIME_ZONE', 'Europe/Lisbon')

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

MEDIA_ROOT = BASE_DIR / 'media'
MEDIA_URL = '/media/'
STATIC_ROOT = BASE_DIR / 'static'
STATIC_URL = '/static/'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# CSRF
CSRF_TRUSTED_ORIGINS = *list(itertools.chain(
    *[[f"https://{i}", f"https://www.{i}"] for i in
      json.loads(os.environ.get('ALLOWED_HOSTS'))])),

# CORS
# ------------------------------------------------------------------------------
# See https://pypi.org/project/django-cors-headers/

CORS_ORIGIN_ALLOW_ALL = DEBUG
CORS_ORIGIN_WHITELIST = CSRF_TRUSTED_ORIGINS
CORS_ALLOW_HEADERS = (
    *default_headers,
    # Sentry trace propagation targets
    "sentry-trace",
    "baggage",
)

# Websockets
# ------------------------------------------------------------------------------

REDIS_CHANNELS_URL = os.environ.get('REDIS_CHANNELS_URL', 'redis://redis:6379/0')
ASGI_APPLICATION = "config.asgi.application"

# Configure channel layers with Redis fallback to InMemory
if DEBUG and os.environ.get('USE_REDIS_CHANNELS', 'true').lower() == 'false':
    # Force InMemory for development/testing
    CHANNEL_LAYERS = {
        "default": {
            "BACKEND": "channels.layers.InMemoryChannelLayer"
        }
    }
else:
    # Try Redis first, fallback to InMemory if it fails
    try:
        CHANNEL_LAYERS = {
            "default": {
                "BACKEND": "channels_redis.core.RedisChannelLayer",
                "CONFIG": {
                    "hosts": [REDIS_CHANNELS_URL],
                    "capacity": 1500,
                    "expiry": 60,
                    # Add connection timeout and retry settings
                    "connection_kwargs": {
                        "socket_connect_timeout": 10,
                        "socket_timeout": 10,
                        "retry_on_timeout": True,
                        "health_check_interval": 30,
                        "socket_keepalive": True,
                        "socket_keepalive_options": {},
                    },
                },
            },
        }

        # Add SSL configuration for production Redis if using rediss://
        if REDIS_CHANNELS_URL.startswith('rediss://'):
            try:
                import ssl
                CHANNEL_LAYERS["default"]["CONFIG"]["connection_kwargs"].update({
                    "ssl_cert_reqs": ssl.CERT_NONE,
                    "ssl_check_hostname": False,
                })
                import logging
                logger = logging.getLogger(__name__)
                logger.info("Configured Redis with SSL for channels")
            except Exception as ssl_error:
                # Log the error but don't crash the application
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Failed to configure Redis SSL: {ssl_error}")

                # Fallback to in-memory channel layer
                CHANNEL_LAYERS = {
                    "default": {
                        "BACKEND": "channels.layers.InMemoryChannelLayer"
                    }
                }
                logger.warning("Falling back to InMemoryChannelLayer due to Redis SSL issues")
        else:
            # Non-SSL Redis connection
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"Configured Redis without SSL for channels: {REDIS_CHANNELS_URL[:30]}...")

    except Exception as channel_error:
        # Fallback configuration if Redis is completely unavailable
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to configure channel layers: {channel_error}")

        # Use in-memory channel layer as fallback
        CHANNEL_LAYERS = {
            "default": {
                "BACKEND": "channels.layers.InMemoryChannelLayer"
            }
        }
        logger.warning("Using InMemoryChannelLayer as fallback")

# MANAGER CONFIGURATION
# ------------------------------------------------------------------------------
STAFF = os.environ.get("STAFF")
ADMIN_PASSWORD = os.environ.get("ADMIN_PASSWORD")
TEACHER_PASSWORD = os.environ.get("TEACHER_PASSWORD")

CMS_URL = os.environ.get("CMS_URL", "http://0.0.0.0:1773")
CMS_MAGIC_KEY = os.environ.get("CMS_MAGIC_KEY", "testMagicKey")
DIGITAL_OCEAN_FUNCTIONS_AUTH_TOKEN = os.environ.get(
    "DIGITAL_OCEAN_FUNCTIONS_AUTH_TOKEN", "testMagicKey")
BASE_FRONTEND_URL = os.environ.get("BASE_FRONTEND_URL")

# Meilisearch
MEILISEARCH_URL = os.environ.get("MEILISEARCH_URL", "http://0.0.0.0:7700")
MEILISEARCH_API_TOKEN = os.environ.get("MEILISEARCH_API_TOKEN", "masterKey")

# Marketing Tools
MARKETING_TRIGGERS_ENABLED = os.environ.get(
    'MARKETING_TRIGGERS_ENABLED', False) == 'True'

# Hubspot
HUBSPOT_ACCESS_TOKEN = os.environ.get("HUBSPOT_ACCESS_TOKEN")
HUBSPOT_NEWSLETTER_GENERAL_LIST_ID = os.environ.get("HUBSPOT_NEWSLETTER_GENERAL_LIST_ID")
HUBSPOT_NEWSLETTER_GLOBAL_USERS_LIST_ID = os.environ.get("HUBSPOT_NEWSLETTER_GLOBAL_USERS_LIST_ID")
HUBSPOT_FREE_MEMBERS_LIST_ID = os.environ.get("HUBSPOT_FREE_MEMBERS_LIST_ID")
HUBSPOT_COMMUNITY_MONTHLY_LIST_ID = os.environ.get("HUBSPOT_COMMUNITY_MONTHLY_LIST_ID")
HUBSPOT_COMMUNITY_YEARLY_LIST_ID = os.environ.get("HUBSPOT_COMMUNITY_YEARLY_LIST_ID")
HUBSPOT_THRIVE_MONTHLY_LIST_ID = os.environ.get("HUBSPOT_THRIVE_MONTHLY_LIST_ID")
HUBSPOT_THRIVE_YEARLY_LIST_ID = os.environ.get("HUBSPOT_THRIVE_YEARLY_LIST_ID")
HUBSPOT_GIFT_GIVERS_LIST_ID = os.environ.get("HUBSPOT_GIFT_GIVERS_LIST_ID")
HUBSPOT_GIFT_RECEIVERS_LIST_ID = os.environ.get("HUBSPOT_GIFT_RECEIVERS_LIST_ID")
HUBSPOT_INTERESTS_LIST_ID = os.environ.get("HUBSPOT_INTERESTS_LIST_ID")
HUBSPOT_ABANDONED_CART_YEARLY_LIST_ID = os.environ.get("HUBSPOT_ABANDONED_CART_YEARLY_LIST_ID")
HUBSPOT_ABANDONED_CART_YEARLY_COUPON_CODE = os.environ.get("HUBSPOT_ABANDONED_CART_YEARLY_COUPON_CODE")
HUBSPOT_ABANDONED_CART_QUARTERLY_LIST_ID = os.environ.get("HUBSPOT_ABANDONED_CART_QUARTERLY_LIST_ID")
HUBSPOT_ABANDONED_CART_QUARTERLY_COUPON_CODE = os.environ.get("HUBSPOT_ABANDONED_CART_QUARTERLY_COUPON_CODE")
HUBSPOT_ABANDONED_CART_PURCHASE_LIST_ID = os.environ.get("HUBSPOT_ABANDONED_CART_PURCHASE_LIST_ID")

# Chargebee
CHARGEBEE_API_KEY = os.environ.get("CHARGEBEE_API_KEY")
CHARGEBEE_SITE = os.environ.get("CHARGEBEE_SITE")
CHARGEBEE_MAGIC_KEY = os.environ.get("CHARGEBEE_MAGIC_KEY")
CHARGEBEE_FREE_PLAN_ID = os.environ.get("CHARGEBEE_FREE_PLAN_ID")
CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS = [entry for entry in
      json.loads(os.environ.get('CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS'))]
# CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS = os.environ.get("CHARGEBEE_COMMUNITY_MONTHLY_PLAN_ID")
CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS = [entry for entry in
      json.loads(os.environ.get('CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS'))]
# CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS = os.environ.get("CHARGEBEE_COMMUNITY_YEARLY_PLAN_ID")
CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS = [entry for entry in
      json.loads(os.environ.get('CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS'))]
# CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS = os.environ.get("CHARGEBEE_THRIVE_MONTHLY_PLAN_ID")
CHARGEBEE_THRIVE_YEARLY_PLAN_IDS = [entry for entry in
      json.loads(os.environ.get('CHARGEBEE_THRIVE_YEARLY_PLAN_IDS'))]
# CHARGEBEE_THRIVE_YEARLY_PLAN_IDS = os.environ.get("CHARGEBEE_THRIVE_YEARLY_PLAN_ID")
CHARGEBEE_COMMUNITY_COURSES_DISCOUNT_COUPON_ID = os.getenv('CHARGEBEE_COMMUNITY_COURSES_DISCOUNT_COUPON_ID')
CHARGEBEE_SUBSCRIPTION_GIFT_ID = os.getenv('CHARGEBEE_SUBSCRIPTION_GIFT_ID')
CHARGEBEE_GIFT_PREFIX = os.getenv('CHARGEBEE_GIFT_COUPON_PREFIX')
CHARGEBEE_ABANDONED_CART_DISCOUNT_PERCENTAGE = os.getenv('CHARGEBEE_ABANDONED_CART_DISCOUNT_PERCENTAGE')
CHARGEBEE_ABANDONED_CART_DISCOUNT_VALID_DAYS = os.getenv('CHARGEBEE_ABANDONED_CART_DISCOUNT_VALID_DAYS')

# Mux
MUX_ACCESS_TOKEN_ID = os.environ.get("MUX_ACCESS_TOKEN_ID")
MUX_SECRET_KEY = os.environ.get("MUX_SECRET_KEY")

# Digital Ocean Spaces
AWS_ACCESS_KEY_ID = os.getenv('DIGITAL_OCEAN_ACCESS_KEY')
AWS_SECRET_ACCESS_KEY = os.getenv('DIGITAL_OCEAN_SECRET_KEY')
AWS_REGION = os.environ.get('DIGITAL_OCEAN_REGION')
AWS_STORAGE_BUCKET_NAME = os.getenv('DIGITAL_OCEAN_BUCKET_NAME')
AWS_DEFAULT_ACL = 'public-read'
AWS_S3_ENDPOINT_URL = f"https://{AWS_STORAGE_BUCKET_NAME}.{AWS_REGION}.digitaloceanspaces.com"
AWS_S3_OBJECT_PARAMETERS = {'CacheControl': 'max-age=86400'}
# static settings
AWS_STATIC_LOCATION = 'static'
# public media settings
PUBLIC_MEDIA_LOCATION = 'media'
# private media settings
PRIVATE_MEDIA_LOCATION = 'private'
PRIVATE_FILE_STORAGE = 'config.storage.PrivateMediaStorage'
if os.getenv('USE_DIGITAL_OCEAN_SPACES', False) == 'True':
    STATIC_URL = f"{AWS_S3_ENDPOINT_URL}/{AWS_STATIC_LOCATION}/"
    MEDIA_URL = f"{AWS_S3_ENDPOINT_URL}/{PUBLIC_MEDIA_LOCATION}/"
    DEFAULT_FILE_STORAGE = 'config.storage.PublicMediaStorage'

# Community Forum
NODEBB_ENABLED = os.getenv("NODEBB_ENABLED", "0") == "1"
NODEBB_API_TOKEN = os.getenv("NODEBB_API_TOKEN")
NODEBB_JWT_SECRET = os.getenv("NODEBB_JWT_SECRET")
NODEBB_API_UID = os.getenv("NODEBB_API_UID")
NODEBB_COMMUNITY_URL = os.getenv("NODEBB_COMMUNITY_URL")
NODEBB_COMMUNITY_GROUPS = json.loads(os.environ.get('NODEBB_COMMUNITY_INITIAL_GROUP_NAMES'))

# Avatar
AVATAR_GENERATOR_URL = os.getenv("AVATAR_GENERATOR_URL")

# Zoom
ZOOM_CLIENT_ID = os.getenv("ZOOM_CLIENT_ID")
ZOOM_CLIENT_SECRET = os.getenv("ZOOM_CLIENT_SECRET")
ZOOM_SECRET_TOKEN = os.getenv("ZOOM_SECRET_TOKEN")
ZOOM_VERIFICATION_TOKEN = os.getenv("ZOOM_VERIFICATION_TOKEN")
