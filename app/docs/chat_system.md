## User Profile Integration

When viewing a user profile, the API now includes a `chatThreadId` field if a private chat thread already exists between the current user and the profile being viewed:

```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "location": "New York",
  "picture": "https://example.com/media/profiles/jane_smith.jpg",
  "picture_tiny": "https://example.com/media/profiles/jane_smith_tiny.jpg",
  "biography": "Product designer and yoga enthusiast",
  "interests": ["Design", "Yoga", "Travel"],
  "clubsCount": 3,
  "coursesCount": 5,
  "connectionsCount": 120,
  "memberSince": "2022",
  "social_networks": {
    "twitter": "janesmith",
    "linkedin": "jane-smith"
  },
  "isFollowing": false,
  "chatThreadId": "456e7890-f12d-34e5-a678-901234567890"
}
```

If `chatThreadId` is present, the frontend should:
1. Display a "Message" button that navigates directly to the existing chat thread
2. Use the provided thread ID to connect to the WebSocket: `ws://localhost:8000/ws/chat/{chatThreadId}/?token=YOUR_ACCESS_TOKEN`

If `chatThreadId` is null or not present, the frontend should:
1. Display a "Message" button that creates a new thread when clicked
2. Use the direct message endpoint to create a new thread: `POST /api/chat/threads/direct_message/`

This optimization eliminates the need for the frontend to check if a thread exists before creating a new one, improving the user experience and reducing API calls.

## Message Pagination

The chat system supports simple offset/limit pagination for retrieving messages:

### 1. Thread Messages Endpoint

- **Endpoint**: `GET /api/chat/threads/{thread_id}/messages/?limit=30&offset=0`
- **Query Parameters**:
  - `limit`: Number of messages per page (default: 30)
  - `offset`: Number of messages to skip (default: 0)
- **Response**:
  ```json
  {
    "results": [
      {
        "id": "abcdef12-3456-7890-abcd-ef1234567890",
        "thread": "123e4567-e89b-12d3-a456-426614174000",
        "author": 1,
        "author_details": {...},
        "body": "Hello, how are you?",
        "attachment": null,
        "attachment_url": null,
        "created": "2023-05-29T10:00:01Z"
      },
      // More messages...
    ],
    "total": 150,
    "limit": 30,
    "offset": 0
  }
  ```

### 2. Alternative Messages Endpoint

You can also use the general messages endpoint with thread filtering:

- **Endpoint**: `GET /api/chat/messages/?thread={thread_id}&limit=30&offset=0`
- **Query Parameters**:
  - `thread`: Thread ID (required)
  - `limit`: Number of messages per page (default: 30)
  - `offset`: Number of messages to skip (default: 0)
- **Response**: Same format as the dedicated messages endpoint

### Implementation Notes

- Messages are returned in reverse chronological order (newest first)
- The first page of messages (offset=0) is cached for performance
- The `total` field indicates the total number of messages in the thread
- To get the next page, increment the offset by the limit value

### Frontend Implementation Example

```javascript
// Load initial thread with recent messages
async function loadThreadWithRecentMessages(threadId) {
  const token = localStorage.getItem('token');
  const response = await fetch(`/api/chat/threads/${threadId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return await response.json();
}

// Load paginated messages
async function loadMessages(threadId, limit = 30, offset = 0) {
  const token = localStorage.getItem('token');
  const response = await fetch(`/api/chat/threads/${threadId}/messages/?limit=${limit}&offset=${offset}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return await response.json();
}

// Load next page of messages
async function loadNextPage(threadId, currentResponse) {
  const nextOffset = currentResponse.offset + currentResponse.limit;
  if (nextOffset >= currentResponse.total) {
    return null; // No more messages
  }
  
  return await loadMessages(threadId, currentResponse.limit, nextOffset);
}
```
