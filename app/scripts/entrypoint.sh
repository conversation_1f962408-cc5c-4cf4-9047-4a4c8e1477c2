#!/bin/bash

# Port configuration
APP_PORT=${PORT:-8080}
NUM_WORKERS=${UVICORN_WORKERS:-3}
TIMEOUT=${UVICORN_TIMEOUT:-120}

cd /app/

# Set Django settings module explicitly
export DJANGO_SETTINGS_MODULE=config.settings

# Run database migrations if needed
#python manage.py migrate --noinput

# Collect static files
#python manage.py collectstatic --noinput

# Start Uvicorn with ASGI application
# This will handle both HTTP and WebSocket connections
exec uvicorn config.asgi:application \
  --host 0.0.0.0 \
  --port ${APP_PORT} \
  --workers ${NUM_WORKERS} \
  --timeout-keep-alive ${TIMEOUT} \
  --ws-ping-interval 20 \
  --ws-ping-timeout 20 \
  --proxy-headers \
  --forwarded-allow-ips='*'
