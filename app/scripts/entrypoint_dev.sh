#!/bin/bash

APP_PORT=${PORT:-8000}
NUM_WORKERS=${UVICORN_WORKERS:-1}
LOG_LEVEL=${UVICORN_LOG_LEVEL:-info}
RELOAD=${UVICORN_RELOAD:-true}

cd /app/

# Run migrations
#python manage.py migrate

# Create admin user if needed
#python manage.py create_admin || true

# Start Uvicorn server with ASGI application that includes channels
if [ "$RELOAD" = "true" ]; then
  echo "Starting Uvicorn with auto-reload..."
  uvicorn config.asgi:application \
    --host 0.0.0.0 \
    --port $APP_PORT \
    --ws-ping-interval 20 \
    --ws-ping-timeout 20 \
    --reload \
    --log-level $LOG_LEVEL
else
  echo "Starting Uvicorn with $NUM_WORKERS workers..."
  uvicorn config.asgi:application \
    --host 0.0.0.0 \
    --port $APP_PORT \
    --workers $NUM_WORKERS \
    --ws-ping-interval 20 \
    --ws-ping-timeout 20 \
    --log-level $LOG_LEVEL
fi
